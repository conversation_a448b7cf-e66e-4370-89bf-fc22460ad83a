<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Profile - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/profile.css">
</head>
<body>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscribe.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>

            <!-- Hamburger Menu Button -->
            <button type="button" class="hamburger" id="hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="menu" id="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy" aria-current="page">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html" aria-current="page">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </header>
    <!-- Move hero section OUTSIDE of .profile-container for full width -->
    <section class="profile-hero" aria-label="Profile Hero Section">
        <div class="profile-hero-banner">
            <!-- Optional: Banner image, fallback to gradient if not set -->
            <!-- <img src="imgs/profile-banner.jpg" alt="Profile Banner" class="profile-banner-img"> -->
            <button type="button" class="change-banner-btn" aria-label="Change Banner" title="Change Banner">
                <i class="fas fa-image"></i>
            </button>
        </div>
        <div class="profile-hero-glass-card">
            <div class="profile-hero-header">
                <div class="profile-avatar-ring">
                    <form class="profile-image-form" id="profileImageForm" enctype="multipart/form-data">
                        <label for="profileImageInput" class="profile-hero-avatar-label" title="Change profile picture">
                            <span class="profile-avatar-gradient"></span>
                            <img src="imgs/profile-icon-B.png" alt="Your Profile Picture" class="profile-hero-avatar" id="profileHeroAvatar" loading="lazy">
                            <span class="profile-avatar-edit"><i class="fas fa-camera"></i></span>
                            <input type="file" id="profileImageInput" accept="image/*" hidden>
                        </label>
                    </form>
                </div>
                <div class="profile-hero-userinfo">
                    <div class="profile-hero-title-row">
                        <h1 class="profile-hero-title">Welcome, Username!</h1>
                        <span class="profile-status-badge premium" title="Premium User">
                            <i class="fas fa-crown"></i> Premium
                        </span>
                    </div>
                    <div class="profile-hero-actions">
                        <button type="button" class="profile-settings-btn" aria-label="Profile Settings" onclick="window.location.href='settings.html'">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button type="button" class="profile-share-btn" aria-label="Share Profile">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            <p class="profile-hero-bio" id="profileHeroBio" contenteditable="true" spellcheck="true" aria-label="Edit your bio">
                Click here to add a short bio about yourself...
            </p>
            <div class="profile-followed-by">
                <span><i class="fas fa-users"></i> Followed by <strong>12</strong> users</span>
            </div>
            <div class="profile-stats" role="list" aria-label="Profile statistics">
                <div class="profile-stat-item" role="listitem" data-stat="playlists" tabindex="0" aria-label="12 playlists created">
                    <span class="profile-stat-icon"><i class="fas fa-list-music"></i></span>
                    <span class="profile-stat-number" data-count="12">12</span>
                    <span class="profile-stat-label">Playlists</span>
                </div>
                <div class="profile-stat-item" role="listitem" data-stat="liked" tabindex="0" aria-label="87 liked songs">
                    <span class="profile-stat-icon"><i class="fas fa-heart"></i></span>
                    <span class="profile-stat-number" data-count="87">87</span>
                    <span class="profile-stat-label">Liked Songs</span>
                </div>
                <div class="profile-stat-item" role="listitem" data-stat="following" tabindex="0" aria-label="Following 34 users">
                    <span class="profile-stat-icon"><i class="fas fa-user-friends"></i></span>
                    <span class="profile-stat-number" data-count="34">34</span>
                    <span class="profile-stat-label">Following</span>
                </div>
                <div class="profile-stat-item" role="listitem" data-stat="listening-time" tabindex="0" aria-label="142 hours of listening time">
                    <span class="profile-stat-icon"><i class="fas fa-clock"></i></span>
                    <span class="profile-stat-number" data-count="142">142</span>
                    <span class="profile-stat-label">Hours</span>
                </div>
            </div>
        </div>
    </section>
    <main id="main-content" class="container">
        <!-- ...existing Hero section... -->

        <!-- Favorite Playlist Section -->
        <section class="profile-favorite-section" aria-labelledby="favorite-playlist-title">
            <h2 id="favorite-playlist-title" class="profile-section-title">
                <i class="fas fa-star"></i> Favorite Playlist
            </h2>
            <div class="favorite-card playlist-favorite-card" tabindex="0" aria-label="Chill Beats playlist, 32 tracks, Genre: Lo-fi">
                <div class="favorite-img-container">
                    <img src="imgs/playlist-01.png" alt="Favorite Playlist Cover" class="favorite-cover-img">
                    <div class="favorite-play-overlay">
                        <button type="button" class="favorite-play-btn" aria-label="Play Favorite Playlist">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="favorite-card-content">
                    <div class="favorite-card-title">Chill Beats</div>
                    <div class="favorite-card-meta">
                        <span><i class="fas fa-music"></i> 32 tracks</span>
                        <span class="favorite-card-tag">Lo-fi • Chillhop</span>
                    </div>
                    <a href="#" class="favorite-action-btn" aria-label="Open Playlist">
                        <i class="fas fa-list"></i> View Playlist
                    </a>
                </div>
            </div>
        </section>

        <!-- Favorite Artist Section -->
        <section class="profile-favorite-section" aria-labelledby="favorite-artist-title">
            <h2 id="favorite-artist-title" class="profile-section-title">
                <i class="fas fa-heart"></i> Favorite Artist
            </h2>
            <div class="favorite-card artist-favorite-card" tabindex="0" aria-label="Artist Alpha, Indie Pop">
                <div class="favorite-img-container artist-avatar-container">
                    <img src="imgs/album-01.png" alt="Favorite Artist" class="favorite-artist-avatar">
                    <div class="favorite-play-overlay">
                        <button type="button" class="favorite-play-btn" aria-label="Play Artist">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="favorite-card-content">
                    <div class="favorite-card-title">Artist Alpha</div>
                    <div class="favorite-card-meta">
                        <span class="favorite-card-tag">Indie Pop</span>
                    </div>
                    <a href="explore.html" class="favorite-action-btn" aria-label="View Artist Profile">
                        <i class="fas fa-user"></i> View Artist
                    </a>
                </div>
            </div>
        </section>

        <!-- Recent Activity Section -->
        <section class="profile-activity-section" aria-labelledby="recent-activity-title">
            <h2 id="recent-activity-title" class="profile-section-title">
                <i class="fas fa-clock"></i> Recent Activity
            </h2>
            <div class="activity-feed">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Liked</strong> "Midnight Drive" by Neon Cruiser</p>
                        <span class="activity-time">2 hours ago</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Added</strong> 3 songs to "Chill Beats" playlist</p>
                        <span class="activity-time">5 hours ago</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Started following</strong> Artist Alpha</p>
                        <span class="activity-time">1 day ago</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Created</strong> new playlist "Study Vibes"</p>
                        <span class="activity-time">2 days ago</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Top Genres Section -->
        <section class="profile-genres-section" aria-labelledby="top-genres-title">
            <h2 id="top-genres-title" class="profile-section-title">
                <i class="fas fa-music"></i> Your Top Genres
            </h2>
            <div class="genres-grid">
                <div class="genre-card" data-genre="lo-fi">
                    <div class="genre-icon">🎵</div>
                    <h3>Lo-fi Hip Hop</h3>
                    <p>45% of listening time</p>
                    <div class="genre-progress">
                        <div class="genre-progress-fill" data-width="45"></div>
                    </div>
                </div>
                <div class="genre-card" data-genre="electronic">
                    <div class="genre-icon">⚡</div>
                    <h3>Electronic</h3>
                    <p>28% of listening time</p>
                    <div class="genre-progress">
                        <div class="genre-progress-fill" data-width="28"></div>
                    </div>
                </div>
                <div class="genre-card" data-genre="indie">
                    <div class="genre-icon">🎸</div>
                    <h3>Indie Pop</h3>
                    <p>18% of listening time</p>
                    <div class="genre-progress">
                        <div class="genre-progress-fill" data-width="18"></div>
                    </div>
                </div>
                <div class="genre-card" data-genre="ambient">
                    <div class="genre-icon">🌙</div>
                    <h3>Ambient</h3>
                    <p>9% of listening time</p>
                    <div class="genre-progress">
                        <div class="genre-progress-fill" data-width="9"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recently Played Section -->
        <section class="profile-recent-section" aria-labelledby="recently-played-title">
            <h2 id="recently-played-title" class="profile-section-title">
                <i class="fas fa-history"></i> Recently Played
            </h2>
            <div class="recent-tracks">
                <div class="recent-track-item">
                    <img src="imgs/album-03.png" alt="Sonic Bloom" class="recent-track-cover">
                    <div class="recent-track-info">
                        <h4>Sonic Bloom</h4>
                        <p>Echo Collective</p>
                    </div>
                    <div class="recent-track-time">
                        <span>3:45</span>
                        <button type="button" class="recent-track-play" aria-label="Play Sonic Bloom">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="recent-track-item">
                    <img src="imgs/album-04.png" alt="Urban Canvas" class="recent-track-cover">
                    <div class="recent-track-info">
                        <h4>Urban Canvas</h4>
                        <p>Streetlight Symphony</p>
                    </div>
                    <div class="recent-track-time">
                        <span>4:12</span>
                        <button type="button" class="recent-track-play" aria-label="Play Urban Canvas">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="recent-track-item">
                    <img src="imgs/album-05.png" alt="Midnight Drive" class="recent-track-cover">
                    <div class="recent-track-info">
                        <h4>Midnight Drive</h4>
                        <p>Neon Cruiser</p>
                    </div>
                    <div class="recent-track-time">
                        <span>5:23</span>
                        <button type="button" class="recent-track-play" aria-label="Play Midnight Drive">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="recent-track-item">
                    <img src="imgs/album-06.png" alt="Celestial Journey" class="recent-track-cover">
                    <div class="recent-track-info">
                        <h4>Celestial Journey</h4>
                        <p>Star Voyager</p>
                    </div>
                    <div class="recent-track-time">
                        <span>6:01</span>
                        <button type="button" class="recent-track-play" aria-label="Play Celestial Journey">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Follow Profile Feature (for own profile, show followers/following) -->
        <section class="profile-follow-section" aria-label="Followers and Following">
            <div class="follow-stats">
                <span><i class="fas fa-users"></i> <strong>12</strong> Followers</span>
                <span><i class="fas fa-user-friends"></i> <strong>34</strong> Following</span>
            </div>
            <!-- If viewing another user's profile, show a follow button instead -->
            <!-- <button class="follow-profile-btn"><i class="fas fa-user-plus"></i> Follow</button> -->
        </section>
    </main>
    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>
            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>
            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>
            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/profile.js"></script>

    <!-- Profile Page Mini Player Integration -->
    <script>
        // Profile Page Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Profile page mini player integration ready');

                    // Listen for play buttons in profile
                    document.addEventListener('click', (e) => {
                        const playBtn = e.target.closest('.favorite-play-btn, .recent-track-play');
                        if (playBtn) {
                            e.preventDefault();
                            e.stopPropagation();

                            let trackData;

                            if (playBtn.classList.contains('favorite-play-btn')) {
                                // Favorite playlist or artist
                                const favoriteCard = playBtn.closest('.favorite-card');
                                const title = favoriteCard?.querySelector('.favorite-card-title')?.textContent || 'Unknown';

                                if (favoriteCard?.classList.contains('playlist-favorite-card')) {
                                    trackData = {
                                        preview: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', // Demo audio
                                        title: title,
                                        artist: 'Playlist',
                                        artwork: favoriteCard.querySelector('img')?.src || 'imgs/album-01.png'
                                    };
                                } else {
                                    trackData = {
                                        preview: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3', // Demo audio
                                        title: 'Top Track',
                                        artist: title,
                                        artwork: favoriteCard.querySelector('img')?.src || 'imgs/album-01.png'
                                    };
                                }
                            } else if (playBtn.classList.contains('recent-track-play')) {
                                // Recent track
                                const trackItem = playBtn.closest('.recent-track-item');
                                trackData = {
                                    preview: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3', // Demo audio
                                    title: trackItem?.querySelector('h4')?.textContent || 'Unknown Track',
                                    artist: trackItem?.querySelector('p')?.textContent || 'Unknown Artist',
                                    artwork: trackItem?.querySelector('img')?.src || 'imgs/album-01.png'
                                };
                            }

                            if (trackData) {
                                // Use global mini player
                                window.miniPlayer.playTrack(trackData);
                            }
                        }
                    });
                } else {
                    console.warn('⚠️ Global mini player not available');
                }
            }, 100);
        });
    </script>
</body>
</html>