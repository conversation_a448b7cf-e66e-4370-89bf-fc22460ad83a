<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/settings.css">
    <title>Settings | Banshee Music</title>
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav role="navigation" aria-label="Main navigation">
            <div class="logo">
                <a href="subscribe.html" aria-label="Go to subscription page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="index.html" role="menuitem">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html" aria-current="page">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main id="main-content" class="container">
        <div class="loader" id="loader"></div>
        <h1>Settings</h1>
        <section class="settings-section">
            <h2>Account Settings</h2>
            <p class="section-description">Update your account information and password. Your changes will be saved securely.</p>
            <form id="accountSettingsForm">
                <div class="settings-group profile-picture-group">
                    <label for="profile-picture">Profile Picture</label>
                    <div class="profile-picture-preview">
                        <img id="profile-picture-img" src="imgs/profile-icon-B.png" alt="Profile Picture" width="80" height="80" style="border-radius:50%;object-fit:cover;">
                    </div>
                    <input type="file" id="profile-picture" name="profile-picture" accept="image/*">
                </div>
                <div class="settings-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" value="" required>
                </div>
                <div class="settings-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="" required>
                </div>
                <div class="settings-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" placeholder="Enter new password" autocomplete="off">
                </div>
                <div class="settings-group">
                    <label for="confirm-password">Confirm Password</label>
                    <input type="password" id="confirm-password" name="confirm-password" placeholder="Confirm new password" autocomplete="off">
                </div>
                <div class="settings-group danger-zone">
                    <h3>Danger Zone</h3>
                    <button type="button" id="delete-account" class="danger-button">Delete Account</button>
                </div>
                <button type="submit" class="save-button">Save Changes</button>
            </form>
        </section>
        <section class="settings-section">
            <h2>Preferences</h2>
            <p class="section-description">Customize your app experience, including theme, language, and audio quality preferences.</p>
            <form id="preferencesForm">
                <div class="settings-group">
                    <label for="theme">Theme</label>
                    <select id="theme" name="theme">
                        <option value="dark">Dark</option>
                        <option value="light">Light</option>
                    </select>
                </div>
                <div class="settings-group">
                    <label for="language">Language</label>
                    <select id="language" name="language">
                        <option value="en">English</option>
                        <option value="es">Español</option>
                        <option value="fr">Français</option>
                    </select>
                </div>
                <div class="settings-group">
                    <label for="quality">Audio Quality</label>
                    <select id="quality" name="quality">
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="very-high">Very High</option>
                    </select>
                </div>
                <div class="settings-group">
                    <div class="checkbox-label">
                        <input type="checkbox" id="autoplay" name="autoplay">
                        <label for="autoplay">Autoplay</label>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="checkbox-label">
                        <input type="checkbox" id="notifications" name="notifications" checked>
                        <label for="notifications">Email Notifications</label>
                    </div>
                </div>
                <button type="submit" class="save-button">Save Preferences</button>
            </form>
        </section>
    </main>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>

            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>

            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/settings.js"></script>

    <!-- Settings Page Mini Player Integration -->
    <script>
        // Settings Page Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Settings page mini player integration ready');

                    // Listen for play buttons (if any are added to settings in the future)
                    document.addEventListener('click', (e) => {
                        const playBtn = e.target.closest('.play-btn, .track-play-btn');
                        if (playBtn && playBtn.dataset.preview) {
                            e.preventDefault();
                            e.stopPropagation();

                            const trackData = {
                                preview: playBtn.dataset.preview,
                                title: playBtn.dataset.title || 'Unknown Track',
                                artist: playBtn.dataset.artist || 'Unknown Artist',
                                artwork: playBtn.dataset.artwork || 'imgs/album-01.png'
                            };

                            // Use global mini player
                            window.miniPlayer.playTrack(trackData);
                        }
                    });
                } else {
                    console.warn('⚠️ Global mini player not available');
                }
            }, 100);
        });
    </script>
</body>
</html>
